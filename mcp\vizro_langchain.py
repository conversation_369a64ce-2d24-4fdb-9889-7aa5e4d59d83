import asyncio
from langchain_mcp_adapter import MCPToolkit
from langchain_openai import ChatOpenAI

async def main():
    # Connect to MCP server
    toolkit = await MCPToolkit.from_mcp_server("vizro-mcp", "python", ["mcp/vizro_server.py"])

    # LLM
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

    # Agent with Vizro tools
    from langchain.agents import initialize_agent, AgentType
    agent = initialize_agent(toolkit.get_tools(), llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True)

    # Example Query
    result = await agent.arun("Create a bar chart dashboard of sales data with columns product and revenue.")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
