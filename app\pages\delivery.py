from vizro.models import Page, Layout, Graph, Table
from app.queries import df_delivery
from app.transforms import status_summary

def page(date_from, date_to, plant_id=None):
    d = df_delivery(date_from, date_to, plant_id)
    agg = status_summary(d)
    comp = [
        Graph(figure={
            "data":[
              {"type":"bar","x":agg[agg.status==s]["date"],"y":agg[agg.status==s]["size"],"name":s}
              for s in agg["status"].unique()
            ],
            "layout":{"barmode":"stack","title":"Report Delivery Status (by day)"}
        }),
        Table(data=d.sort_values("status_updated_at", ascending=False).head(200))
    ]
    return Page(title="Delivery", layout=Layout(grid=[[0],[1]]), components=comp)
