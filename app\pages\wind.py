from vizro.models import Page, Layout, Graph, Table, Card
from app.queries import df_wind

def page(date_from, date_to, plant=None):
    df = df_wind(date_from, date_to, plant)
    comp = [
        Card(title="Wind KPIs", value=f"ΔGen%={(((df.edit_generation-df.generation)/df.generation).mean()*100):.2f}%"),
        Graph(figure={
            "data":[
              {"type":"bar","x":df["date"],"y":df["generation"],"name":"Gen (orig)"},
              {"type":"bar","x":df["date"],"y":df["edit_generation"],"name":"Gen (edit)"}
            ],
            "layout":{"barmode":"group","title":"Daily Generation"}
        }),
        Graph(figure={
            "data":[
              {"type":"scatter","mode":"lines+markers",
               "x":df["date"],"y":df["wind_speed"],"name":"Wind Speed"},
              {"type":"scatter","mode":"lines+markers",
               "x":df["date"],"y":df["edit_wind_speed"],"name":"Wind Speed (edit)"}
            ],
            "layout":{"title":"Wind Speed vs Date"}
        }),
        Table(data=df.sort_values("date", ascending=False).head(100))
    ]
    return Page(title="Wind", layout=Layout(grid=[[0],[1],[2],[3]]), components=comp)
