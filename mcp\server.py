from mcp.server.fastmcp import FastMCPServer
import vizro
import pandas as pd

# Example: simple dataset
data = pd.DataFrame({
    "Category": ["A", "B", "C"],
    "Value": [10, 30, 60]
})

mcp = FastMCPServer("vizro-mcp")

@mcp.tool()
def create_dashboard(title: str = "Demo Dashboard") -> str:
    """
    Creates a Vizro dashboard with sample data.
    Args:
        title: Title of the dashboard
    Returns:
        str: Confirmation message
    """
    app = vizro.Dashboard(
        title=title,
        pages=[
            vizro.Page(
                title="Overview",
                components=[
                    vizro.Component(
                        type="plotly",
                        figure=data.plot(kind="bar", x="Category", y="Value").get_figure()
                    )
                ]
            )
        ]
    )

    app.run()  # launches dashboard locally
    return f"Dashboard '{title}' created and running."

if __name__ == "__main__":
    mcp.run()
