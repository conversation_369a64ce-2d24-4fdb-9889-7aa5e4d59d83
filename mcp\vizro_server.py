import asyncio
from mcp.server.fastmcp import FastMCP
from vizro import VizroApp
import vizro.models as vm

# Initialize MCP server
mcp = FastMCP("vizro-mcp")

# Tool: Create dashboard
@mcp.tool()
async def create_dashboard(title: str, chart_type: str, data: list[dict]):
    """
    Creates a Vizro dashboard with a given chart type.
    Args:
        title (str): Dashboard title
        chart_type (str): e.g. 'bar', 'line', 'pie'
        data (list[dict]): Input data records
    Returns:
        str: Dashboard URL
    """

    # Convert dict list to DataFrame
    import pandas as pd
    df = pd.DataFrame(data)

    # Choose chart dynamically
    if chart_type == "bar":
        chart = vm.Graph(figure=vm.Figure(type="bar", x=df.columns[0], y=df.columns[1], data_frame=df))
    elif chart_type == "line":
        chart = vm.Graph(figure=vm.Figure(type="line", x=df.columns[0], y=df.columns[1], data_frame=df))
    elif chart_type == "pie":
        chart = vm.Graph(figure=vm.Figure(type="pie", names=df.columns[0], values=df.columns[1], data_frame=df))
    else:
        return f"Chart type {chart_type} not supported."

    # Build dashboard
    app = VizroApp(vm.Page(title=title, components=[chart]))
    app.run()  # launches the dashboard locally

    return "Dashboard started at http://127.0.0.1:8050"

if __name__ == "__main__":
    asyncio.run(mcp.run())
