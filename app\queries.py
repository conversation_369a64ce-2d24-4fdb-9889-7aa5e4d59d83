from datetime import date
from app.db import read_sql_df

def df_solar(date_from: date, date_to: date, plant: str|None=None):
    sql = """
    SELECT date, plant_short_name, plant_long_name,
           generation, pr, poa,
           generation_monthly, pr_monthly, poa_monthly,
           edit_generation, edit_pr, edit_poa,
           edit_generation_monthly, edit_pr_monthly, edit_poa_monthly,
           approved, review, status, regenerate, dgr_path, saved_count
    FROM dgr_solar_db
    WHERE date BETWEEN :d1 AND :d2
      AND (:plant IS NULL OR plant_short_name = :plant)
    """
    return read_sql_df(sql, {"d1": date_from, "d2": date_to, "plant": plant})

def df_wind(date_from, date_to, plant=None):
    sql = """
    SELECT date, plant_short_name, plant_long_name,
           generation, wind_speed,
           generation_monthly, wind_speed_monthly,
           edit_generation, edit_wind_speed,
           edit_generation_monthly, edit_wind_speed_monthly,
           csv_report_data, edit_csv_report_data,
           approved, review, status, regenerate, dgr_path, saved_count
    FROM dgr_wind_db
    WHERE date BETWEEN :d1 AND :d2
      AND (:plant IS NULL OR plant_short_name = :plant)
    """
    return read_sql_df(sql, {"d1": date_from, "d2": date_to, "plant": plant})

def df_both(date_from, date_to, plant_solar=None, plant_wind=None):
    sql = """
    SELECT *
    FROM dgr_both_db
    WHERE date BETWEEN :d1 AND :d2
      AND (:ps IS NULL OR plant_short_name_solar = :ps)
      AND (:pw IS NULL OR plant_short_name_wind  = :pw)
    """
    return read_sql_df(sql, {"d1": date_from, "d2": date_to,
                             "ps": plant_solar, "pw": plant_wind})

def df_delivery(date_from, date_to, plant_id=None):
    sql = """
    SELECT r.message_id, r.recipient_id, r.status, r.status_updated_at,
           r.send_date, r.report_date, r.plant_id, r.client_name, r.type,
           r.combined, r.contact_person
    FROM report_status r
    WHERE r.report_date BETWEEN :d1 AND :d2
      AND (:plant_id IS NULL OR r.plant_id = :plant_id)
    """
    return read_sql_df(sql, {"d1": date_from, "d2": date_to, "plant_id": plant_id})
