import asyncio
from langchain_mcp_adapter import MCPAdapter
from langchain.agents import initialize_agent, AgentType
from langchain_openai import ChatOpenAI

async def main():
    # Wrap MCP server
    vizro_tool = await MCPAdapter.from_mcp_config(
        tool_name="vizro",
        command=["python", "mcp/server.py"]
    )

    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

    agent = initialize_agent(
        tools=[vizro_tool],
        llm=llm,
        agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
        verbose=True,
    )

    # Example: LLM asks MCP to create a dashboard
    response = await agent.arun("Create a sales dashboard using vizro")
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
