import pandas as pd

def edited_vs_original(df: pd.DataFrame, cols_pairs: dict):
    out = {}
    for name, (orig, edit) in cols_pairs.items():
        if orig in df and edit in df:
            tmp = df[[orig, edit]].copy()
            tmp["delta"] = df[edit] - df[orig]
            tmp["delta_pct"] = (tmp["delta"] / df[orig]).replace([pd.NA, pd.NaT], 0)*100
            out[name] = tmp
    return out

def normalize_wind_csv_column(df: pd.DataFrame, column: str):
    # csv_report_data may contain JSON-like string; try json first, else CSV parse
    import json, io
    rows = []
    for raw in df[column].dropna():
        try:
            data = json.loads(raw)
            if isinstance(data, list): rows.extend(data)
            elif isinstance(data, dict): rows.append(data)
        except Exception:
            try:
                rows.extend(pd.read_csv(io.StringIO(raw)).to_dict("records"))
            except Exception:
                pass
    return pd.DataFrame(rows)

def status_summary(delivery_df: pd.DataFrame):
    # counts by status over time
    return (delivery_df
            .assign(date=delivery_df["report_date"].astype("datetime64[ns]").dt.date)
            .groupby(["date","status"], as_index=False)
            .size())
