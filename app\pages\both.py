from vizro.models import Page, Layout, Graph, Card, Table
from app.queries import df_both

def page(date_from, date_to, plant_solar=None, plant_wind=None):
    df = df_both(date_from, date_to, plant_solar, plant_wind)
    comp = [
        Card(title="Combined Snapshot", value=f"Rows={len(df)} | Approved={int(df['approved'].sum()) if 'approved' in df else 0}"),
        Graph(figure={
            "data":[
              {"type":"bar","x":df["date"],"y":df["generation_solar"],"name":"Solar (orig)"},
              {"type":"bar","x":df["date"],"y":df["edit_generation_solar"],"name":"Solar (edit)"},
              {"type":"bar","x":df["date"],"y":df["generation_wind"],"name":"Wind (orig)"},
              {"type":"bar","x":df["date"],"y":df["edit_generation_wind"],"name":"Wind (edit)"},
            ],
            "layout":{"barmode":"group","title":"Solar vs Wind (Edited vs Original)"}
        }),
        Table(data=df.sort_values("date", ascending=False).head(100))
    ]
    return Page(title="Combined", layout=Layout(grid=[[0],[1],[2]]), components=comp)
