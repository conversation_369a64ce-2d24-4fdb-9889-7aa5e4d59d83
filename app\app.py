from datetime import date, timedelta
from vizro import Vizro
from vizro.models import App, ControlBar, Dropdown, DateRangePicker, Navigation
from app.pages import solar, wind, both, delivery

# default range: last 14 days
today = date.today()
d1, d2 = today - timedelta(days=14), today

# Global filters (you can wire these into page calls if you want reactive actions)
controls = ControlBar(
    controls=[
        DateRangePicker(id="date_range", value=[str(d1), str(d2)]),
        Dropdown(id="solar_plant", title="Solar Plant", values=[]),  # fill choices from DB if needed
        Dropdown(id="wind_plant", title="Wind Plant", values=[]),
        Dropdown(id="plant_id", title="Delivery Plant ID", values=[]),
    ]
)

pages = [
    solar.page(d1, d2, plant=None),
    wind.page(d1, d2, plant=None),
    both.page(d1, d2, plant_solar=None, plant_wind=None),
    delivery.page(d1, d2, plant_id=None)
]

app = App(title="Daily Generation Reports", pages=pages, navigation=Navigation(), controls=controls)
Vizro().build(app).run()  # dev server
