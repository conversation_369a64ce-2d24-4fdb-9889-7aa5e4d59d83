import pandas as pd
from vizro import Vizro
from vizro.models import Page, Layout, Graph, Card, Table, ControlBar, Dropdown, DateRangePicker, Navigation
from app.queries import df_solar
from app.transforms import edited_vs_original

def page(date_from, date_to, plant=None):
    data = df_solar(date_from, date_to, plant)
    kpis = {
        "Daily Gen Δ%": round(((data["edit_generation"] - data["generation"]) / data["generation"]).mean()*100, 2),
        "PR Δ%": round(((data["edit_pr"] - data["pr"]) / data["pr"]).mean()*100, 2),
        "POA Δ%": round(((data["edit_poa"] - data["poa"]) / data["poa"]).mean()*100, 2),
    }
    comp = [
        Card(title="Solar KPIs (Edited vs Original)",
             value=f"{kpis['Daily Gen Δ%']}% | PR {kpis['PR Δ%']}% | POA {kpis['POA Δ%']}%"),
        Graph(figure={
            "data":[
              {"type":"bar","x":data["date"],"y":data["generation"],"name":"Gen (orig)"},
              {"type":"bar","x":data["date"],"y":data["edit_generation"],"name":"Gen (edit)"}
            ],
            "layout":{"barmode":"group","title":"Daily Generation"}
        }),
        Graph(figure={
            "data":[
              {"type":"scatter","mode":"markers",
               "x":data["poa"],"y":data["pr"],"name":"PR vs POA (orig)"},
              {"type":"scatter","mode":"markers",
               "x":data["edit_poa"],"y":data["edit_pr"],"name":"PR vs POA (edit)"}
            ],
            "layout":{"title":"PR vs POA"}
        }),
        Table(data=data.sort_values("date", ascending=False).head(100))
    ]
    return Page(title="Solar", layout=Layout(grid=[[0],[1],[2],[3]]), components=comp)
